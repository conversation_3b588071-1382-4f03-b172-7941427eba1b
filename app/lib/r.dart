/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-21 10:14:09
 * @Description  : 资源文件
 * 
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-07-15 15:51:31
 * @FilePath     : /flutter_metatel/lib/r.dart
 */
import 'core/values/config.dart';

class R {
  static const String appName = 'Flutter Metatel';
  static const String appVersion = '1.0.0 bate';
  static const String appDescription = 'Flutter Metatel';
  static const String appAuthor = 'Daen';
  static const String appEmail = '';
  static const String stickerAssertPrefix = "assets/images/sticker/";
  static const String stickerRabbitAssertPrefix = "assets/images/sticker/rabbit/";
  static const String stickerEmojiRabbitAssertPrefix = "assets/images/sticker/emo/";
  static const String assertPrefix = "assets/images/";
  static const String defaultSplashLogo = 'assets/images/skin_splash_logo.png';

  static const String defaultBackground = 'assets/images/default_backgroud.png';

  static const String lottieDustbinGrey = 'assets/lottie/dustbin_grey.json';
  static String splashValue = 'assets/images/splash_value.png';
  static String splashLogo = 'assets/images/bg_splash_logo_hw.png';
  static const String contactorChecked =
      'assets/images/skin_ringtone_valid.png';
  static const String contactorDefault =
      'assets/images/skin_ringtone_invalid.png';
  static String mainSessionIcon = 'assets/images/iv_home_message_oversea.png';
  static String mainSessionActiveIcon = 'assets/images/iv_home_message_selected_oversea.png';
  static String mainContactIcon =  'assets/images/iv_home_contact_oversea.png';
  static String mainChannelIcon = 'assets/images/iv_home_channel.png';
  static String mainChannelActiveIcon = 'assets/images/iv_home_channel_selected.png';

  static String mainContactActiveIcon = 'assets/images/iv_home_contact_selected_oversea.png';
  static String mainSettingIcon = 'assets/images/iv_home_mine_oversea.png';
  static String mainSettingActiveIcon = 'assets/images/iv_home_mine_selected_oversea.png';
  static const String mainBrowserIcon =  'assets/images/iv_home_browser_oversea.png';
  static const String mainBrowserActiveIcon = 'assets/images/iv_home_browser_selected_oversea.png';
  static const String mainWalletIcon ='assets/images/iv_home_wallet_oversea.png';
  static const String mainWalletActiveIcon ='assets/images/iv_home_wallet_active_oversea.png';
  static const String mainCollectionsIcon = 'assets/images/iv_home_collection.png';
  static const String mainCollectionsActiveIcon = 'assets/images/iv_home_collection_selected.png';
  static const String mainDaoIcon = 'assets/images/iv_home_dao.png';
  static const String mainDaoActiveIcon = 'assets/images/iv_home_dao_selected.png';
  static const String muteOn = 'assets/images/mute_on.png';
  static const String muteOff = 'assets/images/mute_off.png';
  static const String speakerOn = 'assets/images/speaker_on.png';
  static const String speakerDisable = 'assets/images/speaker_disable.png';

  static const String speakerOff = 'assets/images/speaker_off.png';
  static const String callHungUp = 'assets/images/call_hangup.png';
  static const String callAccept = 'assets/images/call_accept.png';
  static const String switchCamera = 'assets/images/icon_camera.png';
  static const String bgSplash = 'assets/images/bg_splash.png';
  static const String bgSplashHw = 'assets/images/bg_splash_hw.png';
  static const String bgSplashHwEarth = 'assets/images/bg_splash_earth.png';
  static const String bgSplashHwLogo = 'assets/images/bg_splash_logo_hw.png';

  static String bgAccountMainLogoIm = 'assets/images/ico_account_main_logo_hw.png';
  static String bgAccountMainTop =  'assets/images/bg_account_main_top_im.png';
  static String icoCreateAccount = 'assets/images/ico_create_account_im.png';
  static String icoRestoreAccount ='assets/images/ico_restore_account_im.png';
  static const String icoEditHead = 'assets/images/ico_edit_head.png';
  static String icoQrCodeTrans = 'assets/images/ico_qr_code_trans_im.png';
  static String icoNotifySet = 'assets/images/ico_notify_set_im.png';
  static const String nextArrowGrey = 'assets/images/skin_next_icon.png';
  static const String icoMoney = 'assets/images/ico_money.png';
  static String icoAbout = 'assets/images/ico_about_im.png';
  static const String icoDataStorage = 'assets/images/ico_data_storage.png';
  static String icNetwork = 'assets/images/ic_network_im.png';
  static String icoPhoneCallContactDetail = 'assets/images/ico_phone_call_contact_detail_im.png';
  static String icoVideoCallContactDetail ='assets/images/ico_video_call_contact_detail_im.png';
  static String icoMessage = 'assets/images/ico_message_im.png';
  static String icoPhoneCall = 'assets/images/ico_phone_call_im.png';
  static String icoVideoCall = 'assets/images/ico_video_call_im.png';
  static const String icoDelFriend = 'assets/images/ico_del_friend.png';
  static const String icoShareContact = 'assets/images/ico_share_contact.png';
  static const String icoAddFriend = 'assets/images/ico_add_friend.png';
  static String defaultAvatar = 'assets/images/default_avatar_im.png';
  static String groupDefaultAvatar =  'assets/images/default_avatar_channel.png';
  static String privateGroupDefaultAvatar = 'assets/images/default_avatar_group.png';
  static const String icoMyCollect = 'assets/images/ico_my_collect.png';
  static const String file = 'assets/images/file.png';
  static const String videoLoading = 'assets/images/video_loading.jpg';
  static const String icImageLoading = 'assets/images/ico_image_loading.jpg';
  static const String icoScanBlue = 'assets/images/ico_scan_blue.png';
  static const String icoPicLoadFailed =
      'assets/images/ico_pic_load_failed.jpg';
  static const String icoDelete = 'assets/images/ico_delete.png';
  static const String personAdd = 'assets/images/person_add.png';
  static const String shareChannelAirdrop =
      'assets/images/ic_channel_airdrop.png';
  static const String shareChannelContact =
      'assets/images/ic_channel_contact.png';
  static const String shareChannelSave = 'assets/images/ic_channel_save.png';
  static const String shareChannelCopy = 'assets/images/ic_share_copy.png';
  static const String shareChannelSuccess =
      'assets/images/ic_channel_cp_success.png';
  static const String groupAdmin = 'assets/images/group_admin.png';
  static const String icoSelectedRadioListCus =
      'assets/images/ico_selected_radio_list_cus.png';
  static const String icoStickerEmpty = 'assets/images/ico_sticker_empty.jpg';
  static const String icoMore = 'assets/images/ico_more.png';
  static String readDestroy = 'assets/images/read_destroy_im.png';
  static const String readDestroyRed = 'assets/images/read_destroy_red.png';
  static const String icoClose = 'assets/images/ico_close.png';
  static const String icoShare = 'assets/images/ico_share.png';
  static const String icoClean = 'assets/images/ico_clean.png';

  static const String icoCollectionUsed =
      'assets/images/ico_collection_used.png';
  static const String icoStare = 'assets/images/ico_stare.png';
  static String icoQuitApp = 'assets/images/ico_quit_app_im.png';
  static const String icoThemeSet = 'assets/images/ico_theme_set.png';

  static const String callGif = 'assets/images/gif_call.gif';
  static const String callExit = 'assets/images/ic_call_exit.png';
  static const String iconForward = 'assets/images/icon_forward.png';
  static const String icReplayClose = 'assets/images/ic_replay_close.png';
  static const String iconDelete = 'assets/images/icon_delete.png';
  static const String icoComNodeDefault =
      'assets/images/ico_com_node_default.png';
  static const String icoComNodeSelected =
      'assets/images/ico_com_node_selected.png';

  static String
  callVideoS = 'assets/images/ic_video_call_s.png';
  static String
  callVideoR = 'assets/images/ic_video_call_r.png';
  static String
  callAudioS = 'assets/images/ic_audio_call_s.png';
  static String
  callAudioR = 'assets/images/ic_audio_call_r.png';
  static const String bgAccountCreate = 'assets/images/bg_account_create.png';
  static const String icoEditContact = 'assets/images/ico_edit_contact.png';

  static String? icoChatBgSetLabelIm = 'assets/images/ico_chat_bg_set_label_im.png';
  static String? icoFileMangerLabelIm ='assets/images/ico_file_manger_label_im.png';
  static const icInputEmo = 'assets/images/ic_emoticon.png';
  static const icInputSend = 'assets/images/ic_send.png';
  static const icInputImg = 'assets/images/ic_img.png';


  static const icInputEmoSelected = 'assets/images/ic_emoticon_selected.png';
  static const icInputAudio = 'assets/images/ic_input_audio.png';
  static const attachFile = 'assets/images/attach_file.png';
  static const attachFileRed = 'assets/images/attach_file_red.png';
  static const icInputEmoRed = 'assets/images/ic_emoticon_red.png';
  static String bgMineTop = 'assets/images/bg_mine_top.png';
  static String icoAddFriendAppbarIm =
      'assets/images/ico_add_friend_appbar_im.png';
  static String icoEditFriendAppbarIm =
      'assets/images/ico_edit_friend_appbar_im.png';
  static String icoKeyboardDelete = 'assets/images/ico_keyboard_delete.png';

  static String icChatHongBao = 'assets/images/ic_chat__hongbao.png';
  static String icChatFile = 'assets/images/ic_chat_file.png';
  static String icChatPhotos = 'assets/images/ic_chat_photos.png';
  static String icChatPictures = 'assets/images/ic_chat_pictures.png';
  static String icChatVideo = 'assets/images/ic_chat_video.png';
  static String icChatVoice = 'assets/images/ic_chat_voice.png';

  static String icoGroupMsg = 'assets/images/group_msg_im.png';
  static String icoGroupPhone = 'assets/images/group_phone_call_im.png';
  static String icoGroupVideo = 'assets/images/group_video_call_im.png';
  static String icoGroupQuit = 'assets/images/group_quit_group_im.png';
  static String icoGroupDissolution = 'assets/images/group_dissolution_im.png';
  static String icoGroupShare = 'assets/images/ico_share_contact_im.png';

  static String icoPrivacyPolicy =  'assets/images/ico_privacy_policy_im.png';
  static const String icoTermsOfUse = 'assets/images/ico_terms_of_use_im.png';
  static const String icoFeedBack = 'assets/images/ico_feed_back_im.png';
  static const String icoVersion = 'assets/images/ico_version_im.png';
  static const String icoCopy = 'assets/images/ico_copy.png';
  static String icoRecordIM = 'assets/images/recoed_im.gif';
  static String icoRecordButton = 'assets/images/audio_record.png';
  static String icoExpandButton = 'assets/images/expand.png';
  static String icoArrowDown = 'assets/images/ico_arrow_down.png';
  static const String ico_goout = 'assets/images/log_off.png';
  static const String icoTrend = 'assets/images/trend.png';
  static String icoPinSet = 'assets/images/ico_pin_set_im.png';
  static const String icoSearch = 'assets/images/search.png';
  static const String icoDaoAdd = 'assets/images/dao_add.png';
  static const String icoDaoCompanyMenu = 'assets/images/dao_company_menu.png';
  static const String icoDaoPersonalMenu = 'assets/images/dao_personal_menu.png';
  static const String iconDaoUserCounts = 'assets/images/icon_dao_user_count.png';
  static const String icFileHelper = 'assets/images/ic_file_helper.png';
  static const String icDaoEnterprise = 'assets/images/ic_dao_entren.png';
  static const String icDaoAddress = 'assets/images/ic_dao_address.png';
  static const String icoModifyWallerPwd = 'assets/images/ico_modify_waller_pwd.png';
  static const String icoGroupAdmin = 'assets/images/group_admin_ico.png';
  static const String icoGroupOwner = 'assets/images/group_owner.png';
  static const String icToBottom = 'assets/images/ic_to_bottom.png';
  static const String icoFingerprint = 'assets/images/ico_fingerprint.png';
  static const String icoSetProxy = 'assets/images/set_proxy.png';
  static const String icoBottomAdd = 'assets/images/ico_bottom_add.png';
  static const String icoDaoWork = 'assets/images/ico_dao_work.png';
  static const String icoDaoDynamic = 'assets/images/ico_dao_dynamic.png';
  static const String icoDaoCreate = 'assets/images/ico_dao_create.png';
  static const String icoDaoMyAccount = 'assets/images/ico_dao_my_account.png';
  static const String icoDaoPointsManagement = 'assets/images/ico_dao_points_management.png';
  static const String icoDaoCreateCenter = 'assets/images/ico_dao_create_center.png';
  static const String icoDaoMyOrder = 'assets/images/ico_dao_my_order.png';
  static const String icoNoData = 'assets/images/icon_channel_moment_empty.png';
  static const String iconCreateTop = 'assets/images/icon_create_top.png';
  static const String iconCreateLogo = 'assets/images/icon_create_logo.png';
  static const String iconCreateLogoEn = 'assets/images/icon_create_logo_en.png';
  static const String iconCreateNoteWords1 = 'assets/images/icon_note_words_1.png';
  static const String iconCreateNoteWords1En = 'assets/images/icon_note_words_1_en.png';
  static const String iconCreateNoteWords2 = 'assets/images/icon_note_words_2.png';
  static const String iconCreateNoteWords2En = 'assets/images/icon_note_words_2_en.png';
  static const String icoDaoPledge = 'assets/images/ico_dao_pledge.png';
  static String icoDataBackup =  'assets/images/icon_data_backup_oversea.png';
  static const String iconCopyWord = 'assets/images/icon_copy_word.png';
  static const String iconSaveWord = 'assets/images/icon_save_word.png';
  static const String iconCopyWordFill = 'assets/images/icon_copy_word_fill.png';
  static const String iconSaveWordFill = 'assets/images/icon_save_word_fill.png';
  static const String icoBackup = 'assets/images/backup.png';
  static const String icoDao = 'assets/images/dao.png';
  static const String icoPrivate = 'assets/images/private.png';
  static const String iconAnnouncementTop = 'assets/images/icon_announcement_top.png';
  static const String iconHelpCenter = 'assets/images/help_center.png';
  static const String iconFeedback = 'assets/images/feedback.png';
  static String icoBlacklist = 'assets/images/ico_blacklist_im.png';
  static String inviteBg = bg3tDetailOversea;
  static String icoSharePopText = 'assets/images/ico_share_pop_text_im.png';
  static String icoSplitBlue = 'assets/images/ico_split_blue.png';
  static String icoSetInviteBg = 'assets/images/ico_set_invite_bg.png';
  static String icoCloseTrans = 'assets/images/ico_close_trans.png';
  static String icoInviteSetIm = 'assets/images/ico_invite_set_im.png';
  static String icoBindRecommender = 'assets/images/ico_bind_recommender.png';
  static String scan = 'assets/images/scan.png';
  static String shareScan = 'assets/images/share_scan.png';

  static String export = 'assets/images/icon_export.png';

  ///海外我的界面挖矿item背景
  static String bgMinePt = 'assets/images/bg_mine_pt.png';

  ///海外我的界面个人信息item背景
  static String bgMineOversea = 'assets/images/bg_mine_oversea.png';
  static String iconMineRealName = 'assets/images/icon_mine_oversea_real_name.png';
  static String iconMineOverseaExport = 'assets/images/icon_mine_oversea_export.png';
  static String iconMineOverseaQr = 'assets/images/icon_mine_oversea_qr.png';
  static String iconMineOverseaBlock = 'assets/images/icon_mine_oversea_block.png';
  static String iconMineOverseaSet = 'assets/images/icon_mine_oversea_set.png';
  static String iconMineOverseaVersion = 'assets/images/icon_mine_oversea_version.png';
  static String iconMineOverseaShare = 'assets/images/icon_mine_oversea_share.png';
  static String iconMineOverseaNet = 'assets/images/icon_mine_oversea_net.png';
  static String iconMiningSignIn = 'assets/images/icon_mining_sign_in.png';
  static String iconMiningWithdrawHint = 'assets/images/icon_mining_withdraw_hint.png';
  static String bg3tDetailOversea = 'assets/images/bg_3t_detail_oversea.png';
  static String bgMining = 'assets/images/bg_mining_top.png';
  static String bgMiningDetail = 'assets/images/bg_mining_detail.png';
  static String icoMiningTime = 'assets/images/ico_time.png';
  static String iconMiningTaskHead = 'assets/images/icon_mining_task_head.png';
  static String iconMiningInviteHead = 'assets/images/icon_mining_invite_head.png';
  static String icoHelp = 'assets/images/ico_help.png';
  static String iconNotificationPop = 'assets/images/icon_notification_pop.png';
  static String gifLoadingZh = 'assets/images/gif_loading_zh.gif';
  static String gifLoadingEn = 'assets/images/gif_loading_en.gif';
  static String officialImg = 'assets/images/ico_official_avatar.png';
  static String officialSymbol = 'assets/images/ico_official_symbol.png';
  static String icoListPackUp = 'assets/images/ico_list_pack_up.png';
  // static String icoListExpand = 'assets/images/ico_list_expand.png';
  static String icoTop = 'assets/images/top.png';
  static String topMsg = 'assets/images/top_msg.png';
  static String topMsgAdmin = 'assets/images/top_msg_admin.png';
  static String topMsgClose = 'assets/images/top_msg_close.png';

  ///虚拟卡
  static String bgVirtualCard = 'assets/images/bg_virtual_card.png';
  static String bgVirtualAccount = 'assets/images/bg_virtual_account.png';
  static String icoVirtualCardRecord = 'assets/images/ico_virtual_card_record_c.png';
  static String icoVirtualCard = 'assets/images/ico_virtual_card.png';
  static String icoVirtualCardAdd = 'assets/images/ico_virtual_card_add.png';
  static String icoVirtualCardSet = 'assets/images/ico_virtual_card_set.png';
  static String iconTransfer = 'assets/images/icon_transfer.png';
  static String iconCollectMoney = 'assets/images/icon_collect_money.png';
  static String iconSearchRightContact = 'assets/images/icon_search_right_contact.png';
  static String ivSearch = 'assets/images/ic_search.png';


  ///聊天界面长按弹出框
  static String icoChatMenuDialogCopy = 'assets/images/ico_chat_menu_dialog_copy.png';
  static String icoChatMenuDialogDel = 'assets/images/ico_chat_menu_dialog_del.png';
  static String icoChatMenuDialogForward = 'assets/images/ico_chat_menu_dialog_forward.png';
  static String icoChatMenuDialogMulti = 'assets/images/ico_chat_menu_dialog_multi.png';
  static String icoChatMenuDialogMute = 'assets/images/ico_chat_menu_dialog_mute.png';
  static String icoChatMenuDialogMuteNo = 'assets/images/ico_chat_menu_dialog_mute_no.png';
  static String icoChatMenuDialogRepeal = 'assets/images/ico_chat_menu_dialog_repeal.png';
  static String icoChatMenuDialogRepealNo = 'assets/images/ico_chat_menu_dialog_repeal_no.png';
  static String icoChatMenuDialogReply = 'assets/images/ico_chat_menu_dialog_reply.png';
  static String icoChatMenuDialogTop = 'assets/images/ico_chat_menu_dialog_top.png';
  static String icoChatMenuDialogSave = 'assets/images/ico_chat_menu_dialog_save.png';
  static String icoChatMenuDialogQr = 'assets/images/ico_chat_menu_dialog_qr.png';

  static String icSquareCount = 'assets/images/ic_square_count.png';

  ///频道里面标识消息发送方角色
  static String icoChannelAdminTip = 'assets/images/ico_channel_admin_tip.png';
  static String icoChannelOwnerTip = 'assets/images/ico_channel_owner_tip.png';

  ///频道Vip付费属性标识
  static String icoChannelVip = 'assets/images/ico_channel_vip.png';

  ///我的或者设置里面数据备份item图标
  ///数据备份生成的图片底图
  static String bgDataBackupCard = 'assets/images/back_data_bottom.png';
  ///数据备份生成的图片上面个人信息部分背景
  static String bgDataBackupPersonalInfo = 'assets/images/bg_data_backup_personal_info.png';

  static String btCopy = 'assets/images/bt_copy.png';
  static String btSave = 'assets/images/bt_save.png';
  static String btShare = 'assets/images/bt_share.png';

  static String icoCityNodeMedal = 'assets/images/ico_city_node_medal.png';
  static String icoCreateNodeMedal = 'assets/images/ico_create_node_medal.png';
  static String icoMoneyExchange = 'assets/images/ico_money_exchange.png';
  static String icMeetingClose = 'assets/images/ic_meeting_close.png';
  static String icMeetingZoom = 'assets/images/ic_meeting_zoom.png';
  static String icMeeting = 'assets/images/ic_meeting.png';
  static String icSettingPerson = 'assets/images/ico_person.png';
  static String icSettingOtherData = 'assets/images/ico_data.png';
  static String bgMiningComputingZ = 'assets/images/bg_mining_computing_z.png';
  static String bgMiningComputingC = 'assets/images/bg_mining_computing_c.png';
  static String bgEquity = 'assets/images/bg_equity.png';
  static String icEquitySuccess = 'assets/images/icon_equity_success.png';
  static String bgListDid = 'assets/images/bg_did_list.png';
  static String icDidMore = 'assets/images/ic_did_more.png';
  static String icTid = 'assets/images/icon_tid.png';
  static String icTid_d = 'assets/images/ic_tid_d.png';
  static String icTid_t = 'assets/images/ic_tid_t.png';
  static String icAdsClose = 'assets/images/ic_ads_close.png';
  static String icHongBaoClose = 'assets/images/ic_hong_bao_close.png';
  static String bgChatHongBao = 'assets/images/bg_chat_hongbao.png';
  static String icChatHb = 'assets/images/ic_chat_hb.png';
  static String bgChatHongBaoN = 'assets/images/bg_chat_hongbao_n.png';
  static String icCopyLeft = 'assets/images/ic_copy_left.png';
  static String icCopyRight = 'assets/images/ic_copy_right.png';
  static String icShareFresh = 'assets/images/ic_share_fresh.png';
  static String icFileDetail = 'assets/images/ic_file_detail.png';
  static String icFileShare = 'assets/images/ic_file_share.png';
  static String icBigImageShare = 'assets/images/ic_big_image_share.png';
  static String icBigImageEdit = 'assets/images/ic_big_image_edit.png';
  static String icBigImageSend = 'assets/images/ic_big_image_send.png';
  static String icBigImageSave = 'assets/images/ic_big_image_save.png';
  static String icTranslate = 'assets/images/ic_translation.png';
  static String icUnTranslate = 'assets/images/ic_un_translation.png';
  static String icTranslateG = 'assets/images/ic_trans_g.png';
  static String bgNode = 'assets/images/bg_node.png';
  static String icNodeUsed = 'assets/images/ic_node_used.png';
  static String icNodeDefault = 'assets/images/ic_node_default.png';
  static String icNodeSelected = 'assets/images/ic_node_selected.png';
  static String icStakingAdd = 'assets/images/ic_staking_add.png';
  static String icStakingMore = 'assets/images/ic_staking_more.png';
  static String icStaking = 'assets/images/ic_staking.png';
  static String bgStakingItem = 'assets/images/bg_staking_item.png';
  static String icServiceTypeMore = 'assets/images/ic_service_type_more.png';
  static String icAddWallet = 'assets/images/ic_add_staking_wallet.png';
  static String icStakingStatus = 'assets/images/ic_staking_status.png';
  static String icPinEdit = 'assets/images/ic_pin_edit.png';
  static String icPwsOpen = 'assets/images/ic_pwd_open.png';
  static String icPwsClose = 'assets/images/ic_pwd_close.png';
  static String icResetWallet = 'assets/images/ic_reset_staking_wallet.png';
  static String icKeyBoxStatusSuccess = 'assets/images/ic_keybox_status_success.png';
  static String icKeyBoxStatusFailed = 'assets/images/ic_keybox_status_failed.png';
  static String phoneLock = 'assets/images/onboarding/1-phone-lock.png';
  static String tabWarning = 'assets/images/onboarding/1-tab-warning.png';
  static String folder = 'assets/images/onboarding/2-folder.png';
  static String key = 'assets/images/onboarding/2-key.png';
  static String phoneKeyholeWithKey =
      'assets/images/onboarding/2-phone-keyhole-with-key.png';
  static String phoneKeyhole = 'assets/images/onboarding/2-phone-keyhole.png';
  static String leftChain = 'assets/images/onboarding/3-left-chain.png';
  static String padlock = 'assets/images/onboarding/3-padlock.png';
  static String rightChain = 'assets/images/onboarding/3-right-chain.png';
  static String circleShadow = 'assets/images/onboarding/circle-shadow.png';
  static String circle = 'assets/images/onboarding/circle.png';
  static String logoTitle = 'assets/images/onboarding/logo-title.png';
  static String linksayLogo = 'assets/images/onboarding/linksay-logo-01.png';
  static String hornbill = 'assets/images/onboarding/3-hornbill.png';
  static String textbubble1 = 'assets/images/onboarding/3-textbubble-1.png';
  static String onboarding04 = 'assets/images/onboarding/onboarding-04.png';
  static String linksayBubble = 'assets/images/onboarding/3-linksay-bubble.png';
  static String textbubble2 = 'assets/images/onboarding/3-textbubble-2.png';

  static const String iconChannelMoment =
      'assets/images/icon_channel_moment.png';
  static const String iconChannelMomentEmpty =
      'assets/images/icon_channel_moment_empty.png';
  static const String iconEdit = 'assets/images/edit.png';
  static const String iconEditBtn = 'assets/images/icon_edit_btn.png';
  static const String iconDeleteBtn = 'assets/images/icon_delete_btn.png';
  static const String iconShareBtn = 'assets/images/icon_share_btn.png';
  static const String iconCommentBtn = 'assets/images/icon_comment_btn.png';
  static const String iconLikeBtn = 'assets/images/icon_like_btn.png';
  static const String iconLikeBtnSolid =
      'assets/images/icon_like_btn_solid.png';
  static const String addPostBtn = 'assets/images/add_post_btn.png';
  static const String badgeAppStore = 'assets/images/badge_app_store.png';
  static const String badgeGooglePlay = 'assets/images/badge_google_play.png';
  static const String icChatVideoLink = 'assets/images/ic_chat_video_link.png';
  static const String iconMomentShare = 'assets/images/icon_moment_share.png';
  static const String iconMomentSave = 'assets/images/icon_moment_save.png';
  static const String iconDid = 'assets/images/icon_did.png';
  static const String iconGroupNumber = 'assets/images/icon_group_number.png';
  static const String logoVersionUpdate = 'assets/images/logo_version_update.png';
  static const String logoVersionUpdateLogo = 'assets/images/logo_version_update_logo.png';


  static const String iconDeleteAcc = 'assets/images/icon_delete_acc.png';
  static const String iconScanRecommender = 'assets/images/icon_scan_recommender.png';  
  static const String iconDownload = 'assets/images/icon_download.png';  
  static const String iconCopy = 'assets/images/icon_copy.png';  
  static const String iconShare = 'assets/images/icon_share.png';  
  static const String iconRefresh = 'assets/images/icon_refresh.png';  
  static const String iconCopyNew = 'assets/images/icon_copy_new.png'; 
  static const String iconStepPath = 'assets/images/icon_step_path.png';  
  static const String iconCopyNewFill = 'assets/images/icon_copy_new_fill.png';
  static const String iconAudio = 'assets/images/icon_audio.png';
  static const String iconMsg = 'assets/images/icon_msg.png'; 
  static const String iconMeeting = 'assets/images/icon_meeting.png';
  static const String icMoneyPacketBg = 'assets/images/money_packet_bg.png';
  static const String icMoneyPacketOpen = 'assets/images/money_packet_open.png';
  static const String icMoneyPacketUnopen = 'assets/images/money_packet_unopen.png';
  static const String icoSelected = 'assets/images/selected.png';
  static const String icoUnselected = 'assets/images/unselected.png';


  static const String iconMeetingEdit = 'assets/images/icon_meeting_edit.png';
  static const String iconMeetingShare = 'assets/images/icon_meeting_share.png';
  static const String iconMeetingCopy = 'assets/images/icon_meeting_copy.png';
  static const String iconMeetingDetail =
      'assets/images/icon_meeting_detail.png';
  static const String iconMeetingEmpty = 'assets/images/icon_meeting_empty.png';
  static const String iconMeetingCalendar =
      'assets/images/icon_meeting_calendar.png';
  static const String bgMeetingMsg = 'assets/images/bg_meeting_msg.png';
  static const String invitationBg = 'assets/images/invitation_bg.png';
  static const String invitationBgTicket = 'assets/images/invitation_bg_ticket.png';
  static const String invitationGoogleLogo = 'assets/images/invitation_google_logo.png';
  static const String invitationAndroidLogo = 'assets/images/invitation_android_logo.png';
  static const String invitationAppleLogo = 'assets/images/invitation_apple_logo.png';
  static const String invitationWindowsLogo = 'assets/images/invitation_windows_logo.png';
  static const String invitationLinksayLogo = 'assets/images/invitation_linksay_logo.png';
  static const String defaultAvatarIcon = 'assets/images/default_avatar.png';
  static const String btnSwitchIcon = 'assets/images/btn_switch_icon.png';

  static const icCst = 'assets/images/ic_cst.png';
  static const icNext = 'assets/images/ic_next.png';
  static const icCopy = 'assets/images/ic_copy.png';
  static const icCgc = 'assets/images/ic_cgc.png';
  static const icSwap = 'assets/images/ico_swap.png';
  static const ictbnd = 'assets/images/ic_tbnd.png';
  static const icAddNet = 'assets/images/ic_add_net.png';
  static const icKLp = 'assets/images/ic_k_lp.png';
  static const icKSun = 'assets/images/ic_k_sun.png';

  static const iconNetCore = 'assets/images/icon_net_core.png';
  static const iconNetTRX = 'assets/images/icon_net_trx.png';
  static const icoPolygonnew = 'assets/images/ico_polygonnew.png';
  static const icoTether = 'assets/images/ico_tether.png';
  static const icoEth = 'assets/images/ico_eth.png';
  static const iconArbitrum = 'assets/images/icon_eth_new.png';
  static const iconBnb = 'assets/images/icon_bnb.png';
  static const icon3tChain = 'assets/images/ic_3tchain.png';
  static const iconLinkSay = 'assets/images/ic_linksay.png';
  static const icoDefault = 'assets/images/ico_default_token_ico.png';
  static const icon3TToken = 'assets/images/ic_3tchain_token.png';
  static const iconEthChecked = 'assets/images/icon_eth_net_checked.png';
  static const iconTrxChecked = 'assets/images/icon_trx_net_checked.png';
  static const String icChatHbN = 'assets/images/ic_chat_hb_n.png';
  static const String icReal1 = 'assets/images/ic_real_1.png';
  static const String icReal2 = 'assets/images/ic_real_2.png';
  static const String icUpdateClose = 'assets/images/ic_update_close.png';
  static const String icUniappDefault = 'assets/images/ic_uniapp_default.png';
  static const String icUniappMsgLogo = 'assets/images/ic_uniapp_msg_logo.png';
  static const String icRobot = 'assets/images/ic_robot.png';
  static const String agreementBg = 'assets/images/agreement_bg.png';
  static const String agreementTitleBg = 'assets/images/agreement_title_bg.png';
  static const String buttonBg = 'assets/images/button_bg.png';
  static const String headerBg = 'assets/images/header_bg.png';
  static const String headerBgBig = 'assets/images/header_bg_big.png';
  static const String iconLoginRegister = 'assets/images/icon_login_register.png';
  static const String loginscreenBg = 'assets/images/loginscreen_bg.png';
  static const String logoBadge = 'assets/images/logo_badge.png';
  static const String logoGold = 'assets/images/logo_gold.png';
  static const String splashscreenVerticalBg = 'assets/images/splashscreen_vertical_bg.png';
  static const String splashscreenHorizontalBg = 'assets/images/splashscreen_horizontal_bg.png';
  static const String textLogoRed = 'assets/images/text_logo_red.png';
  static const String textLogoTransparent = 'assets/images/text_logo_transparent.png';
  static const String textLogoWhite = 'assets/images/text_logo_white.png';
  static const String btmNavBarBg = 'assets/images/btm_nav_bar_bg.png';
  static const String btmNavIcChat = 'assets/images/btm_nav_ic_chat.png';
  static const String btmNavIcChatActive = 'assets/images/btm_nav_ic_chat_selected.png';
  static const String btmNavIcComm = 'assets/images/btm_nav_ic_comm.png';
  static const String btmNavIcCommActive = 'assets/images/btm_nav_ic_comm_selected.png';
  static const String btmNavIcBrowser = 'assets/images/btm_nav_ic_browser.png';
  static const String btmNavIcBrowserActive = 'assets/images/btm_nav_ic_browser_selected.png';
  static const String btmNavIcMe = 'assets/images/btm_nav_ic_me.png';
  static const String btmNavIcMeActive = 'assets/images/btm_nav_ic_me_selected.png';
  static const String progressBarBg = 'assets/images/progress_bar_bg.png';
  static const String progressBarProgress = 'assets/images/progress_bar_progress.png';
  static const String btmNavIcGameCenter = 'assets/images/btm_nav_ic_game_center.png';
  static const String btmNavIcGamerCenterActive = 'assets/images/btm_nav_ic_game_center_selected.png';
  static const String homeAppbarBg = 'assets/images/home_appbar_bg.png';
  static const String appbarDot = 'assets/images/appbar_dot.png';
  static const String tabbarBg = 'assets/images/tabbar_bg.png';
  static const String tabbarTabBg = 'assets/images/tabbar_tab_bg.png';
  static const String tabbarTabActiveBg = 'assets/images/tabbar_tab_active_bg.png';
  static const String mainBg = 'assets/images/main_bg.png';
  static const String homeAppbarBgBig = 'assets/images/home_appbar_bg_big.png';
  static const String bannerBg = 'assets/images/banner_bg.png';
  static const String tabbarBlockerLeft = 'assets/images/tabbar_blocker_left.png';
  static const String tabbarBlockerRight = 'assets/images/tabbar_blocker_right.png';
  static const String tabbarWoodenRightBg = 'assets/images/tabbar_wooden_right_bg.png';
  static const String tabbarWoodenLeftBg = 'assets/images/tabbar_wooden_left_bg.png';
  static const String tabbarFlag = 'assets/images/tabbar_flag.png';
  static const String commChannelListBg = 'assets/images/comm_channel_list_bg.png';
  static const String commDaoListBg = 'assets/images/comm_dao_list_bg.png';
  static const String commGroupAvatarBg = 'assets/images/comm_group_avatar_bg.png';
  static const String meBg = 'assets/images/me_bg.png';
  static const String meInfoBoardInner = 'assets/images/me_info_board_inner.png';
  static const String meInfoBoard = 'assets/images/me_info_board.png';
  static const String meAvatarBg = 'assets/images/me_avatar_bg.png';
  static const String meChain = 'assets/images/me_chain.png';
  static const String meAvatarEditable = 'assets/images/me_avatar_editable.png';
  static const String woodenSettingItemBg = 'assets/images/wooden_setting_item_bg.png';
  static const String woodenSettingItemDarkBg = 'assets/images/wooden_setting_item_dark_bg.png';
  static const String settingGameCenterIcon = 'assets/images/setting_game_center_icon.png';
  static const String settingShareIcon = 'assets/images/setting_share_icon.png';
  static const String iconCopyOutline = 'assets/images/icon_copy_outline.png';
  static const String nameBoardBg = 'assets/images/name_board_bg.png';
  static const String qrBoardBg = 'assets/images/qr_board_bg.png';
  static const String settingBg = 'assets/images/setting_bg.png';
  static const String settingIconClearCache = 'assets/images/setting_icon_clear_cache.png';
  static const String settingIconNodeRefresh = 'assets/images/setting_icon_node_refresh.png';
  static const String settingIconPin = 'assets/images/setting_icon_pin.png';
  static const String settingIconPrivacyPolicy = 'assets/images/setting_icon_privacy_policy.png';
  static const String settingIconUsageTerm = 'assets/images/setting_icon_usage_term.png';
  static const String settingIconLogout = 'assets/images/setting_icon_logout.png';
  static const String settingIconNotification = 'assets/images/setting_icon_notification.png';
  static const String settingIconPersonalDataCollect = 'assets/images/setting_icon_personal_data_collect.png';
  static const String settingIconThirdPartyShare = 'assets/images/setting_icon_third_party_share.png';
  static const String settingIconUsageFeedback = 'assets/images/setting_icon_usage_feedback.png';
  static const String switchInactiveBg = 'assets/images/switch_inactive_bg.png';
  static const String switchActiveBg = 'assets/images/switch_active_bg.png';
  static const String pinItemFill = 'assets/images/pin_item_fill.png';
  static const String pinItem = 'assets/images/pin_item.png';
  static const String pinBg = 'assets/images/pin_bg.png';
  static const String scrollStyleDialogBg = 'assets/images/scroll_style_dialog_bg.png';
  static const String scrollStyleDialogExit = 'assets/images/scroll_style_dialog_exit.png';
  static const String iconWarning = 'assets/images/icon_warning.png';
  static const String realNameAuthPending = 'assets/images/realname_auth_pending.png';
  static const String realNameAuthDone = 'assets/images/realname_auth_done.png';
  static const String woodenLongBoardBg = 'assets/images/wooden_long_board_bg.png';
  static const String sharePromoteAvatarFrame = 'assets/images/share_promote_avatar_frame.png';
  static const String sharePromoteAvatarBg = 'assets/images/share_promote_avatar_bg.png';
  static const String sharePromoteBg = 'assets/images/share_promote_bg.png';
  static const String sharePromoteCodeBg = 'assets/images/share_promote_code_bg.png';
  static const String sharePromoteCopy = 'assets/images/share_promote_copy.png';
  static const String sharePromoteLogo = 'assets/images/share_promote_logo.png';
  static const String sharePromoteViewList = 'assets/images/share_promote_view_list.png';
  static const String referrerAvatarBg = 'assets/images/referrer_avatar_bg.png';
  static const String qrScanBtnIcon = 'assets/images/qr_scan_btn_icon.png';
  static const String sharePosterBgPart = 'assets/images/share_poster_bg_part.png';
  static const String sharePosterBg = 'assets/images/share_poster_bg.png';
  static const String iconSwap = 'assets/images/icon_swap.png';
  static const String platformPlaystore = 'assets/images/platform_playstore.png';
  static const String platformAndroid = 'assets/images/platform_android.png';
  static const String platformWindow = 'assets/images/platform_window.png';
  static const String platformApple = 'assets/images/platform_apple.png';
  static const String iconInviteNow = 'assets/images/icon_invite_now.png';
  static const String dataBackupBg = 'assets/images/data_backup_bg.png';
  static const String logoTransparent = 'assets/images/logo_transparent.png';
  static const String selectContactConfirmBtn = 'assets/images/select_contact_confirm_btn.png';
  static const String joinPageAvatarFrame = 'assets/images/join_page_avatar_frame.png';
  static const String joinPageContentBg = 'assets/images/join_page_content_bg.png';
  static const String joinPageCountIcon = 'assets/images/join_page_count_icon.png';
  static const String joinPageDescIcon = 'assets/images/join_page_desc_icon.png';
  static const String joinPageHeaderBg = 'assets/images/join_page_header_bg.png';
  static const String joinPageBg = 'assets/images/join_page_bg.png';
  static const String slidableRead = 'assets/images/slidable_read.png';
  static const String slidableUnread = 'assets/images/slidable_unread.png';
  static const String slidableDelete = 'assets/images/slidable_delete.png';
  static const String slidablePin = 'assets/images/slidable_pin.png';
  static const String slidableUnpin = 'assets/images/slidable_unpin.png';
  static const String meetingBg = 'assets/images/meeting_bg.png';
  static const String createMeetingBtn = 'assets/images/create_meeting_btn.png';
  static const String meetingTabActive = 'assets/images/meeting_tab_active.png';
  static const String meetingTab = 'assets/images/meeting_tab.png';
  static const String meetingTabBar = 'assets/images/meeting_tab_bar.png';
  static const String meetingCardHistoryMore = 'assets/images/meeting_card_history_more.png';
  static const String meetingCardMore = 'assets/images/meeting_card_more.png';
  static const String meetingCard = 'assets/images/meeting_card.png';
  static const String meetingCardHistory = 'assets/images/meeting_card_history.png';
  static const String meetingDetail = 'assets/images/meeting_detail.png';
  static const String meetingEdit = 'assets/images/meeting_edit.png';
  static const String meetingShare = 'assets/images/meeting_share.png';
  static const String momentHeaderBoard = 'assets/images/moment_header_board.png';
  static const String momentDescIcon = 'assets/images/moment_desc_icon.png';
  static const String momentBg = 'assets/images/moment_bg.png';
  static const String momentEditBtn = 'assets/images/moment_edit_btn.png';
}
