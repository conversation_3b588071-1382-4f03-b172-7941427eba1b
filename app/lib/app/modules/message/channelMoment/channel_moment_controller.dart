import 'dart:async';

import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_metatel/app/data/models/res/moment/post_res_model.dart';
import 'package:flutter_metatel/app/data/models/user_message_model.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../core/utils/app_log.dart';
import '../../../../core/utils/dio/dio_util.dart';
import '../../../../core/values/colors.dart';
import '../../../../core/values/config.dart';
import '../../../../routes/pages.dart';
import '../../../data/enums/enum.dart';
import '../../../data/models/post.dart';
import '../../../data/providers/api/channel.dart';
import '../../../data/providers/db/database.dart';
import '../../../data/services/channel_service.dart';
import '../../../data/services/config_service.dart';
import '../../group/details/channel_details_controller.dart';
import '../../group/details/group_details_base_controller.dart';

class ChannelMomentController extends GetxController {
  Rx<bool> isCollapsed = false.obs;
  UserMessage? currentUser;
  int? memberCount;
  String _username =
      Get.find<AppConfigService>().getUserName() ?? ''; // 频道动态相关接口Header需传入
  String _groupId = ''; // 频道动态相关接口Header需传入
  RxnString description = RxnString();
  PostPaginationModel? _postPaginationModel;
  RxList<Post> moments = <Post>[].obs;
  RxBool isLoading = false.obs;
  late GroupDetailsBaseController _channelDetailController;
  RefreshController refreshController =
      RefreshController(initialRefresh: false);

  @override
  void onInit() {
    if (Get.arguments != null) {
      currentUser = Get.arguments["current_user"];
      memberCount = Get.arguments["member_count"];
      _groupId = currentUser?.userName ?? '';
    }
    _channelDetailController = Get.put(ChannelDetailsController());
    _channelDetailController.initP();
    _channelDetailController.getChannelInfo(
        _groupId, ChatType.channelChat.index);
    /// 设置Dio的baseUrl为动态的baseUrl
    // DioUtil().setBaseUrl(Config.momentApiBaseUrl());  
    _getMomentInfo();
    _fetchPosts();
    super.onInit();
  }

  @override
  void onClose() {
    refreshController.dispose();
    super.onClose();
  }

  Future<void> onPullToRefresh() async {
    await Future.wait([
      _getMomentInfo(),
      _fetchPosts(),
    ]);
    refreshController.refreshCompleted();
  }

  Future<void> onLoadMore() async {
    if (_postPaginationModel == null) {
      refreshController.loadComplete();
      return;
    }
    final bool isLastPage =
        _postPaginationModel!.currentPage == _postPaginationModel!.totalPage;
    if (isLastPage) {
      refreshController.loadNoData();
      return;
    }
    final nextPage = _postPaginationModel!.currentPage + 1;
    await _fetchPosts(pageNo: "$nextPage");
    refreshController.loadComplete();
  }

  void onDescriptionTap() async {
    if(description.value == null) return;
    await Get.bottomSheet(
      Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r), 
            topRight: Radius.circular(20.r),
          ),
        ),
        padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 20.r),
        child: SingleChildScrollView(
          physics: BouncingScrollPhysics() ,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                L.channel_moment_desc.tr,
                style: TextStyle(
                  fontSize: 15.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 10.r,),
              Text(
                description.value!,
                style: TextStyle(
                  fontSize: 12.sp,                  
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void onEditDescriptionTap() async {
    var updatedDesc = await Get.toNamed(Routes.MomentDescEditView, arguments: description.value);
    if (updatedDesc == null) return;
    EasyLoading.show(maskType: EasyLoadingMaskType.black);
    try {
      var result = await _updateMomentInfo(
          desc: updatedDesc, groupName: currentUser?.displayName ?? '');
      if (!(result ?? false)) {
        EasyLoading.showError(L.update_fail.tr);
        return;
      }
      EasyLoading.showSuccess(L.update_success.tr);
      _getMomentInfo();
    } catch (e) {
      EasyLoading.showError(L.update_fail.tr);
      AppLogger.e("Error Catch<onEditDescriptionTap>: $e");
    }
  }

  void onMemberProfileTap(User? user) {
    if (!isAdminOrOwner()) return;
    if (user == null) return;
    onGoToMemberDetail(username: user.userName);
  }

  void onCreatePostTap() async {
    Post? result = await openCreatePostView();
    if(result == null) return;
    _fetchPosts(); // 创建新帖 成功，刷新页面
  }

  void onEditPostTap(Post post) async {
    Post? result = await openCreatePostView(post: post);  
    if(result == null) return;
    // 编辑帖 成功，更新页面
    refreshSpecificPost(result);        
  }

  void onDeletePostTap(String? postId, {bool needBack = false}) async {
    bool? isConfirm = await showConfirmationDialog(
        L.delete_post_confirmation_desc.tr, L.confirm.tr);
    if (!(isConfirm ?? false)) return;
    if (postId == null || postId.isEmpty) return;
    EasyLoading.show(maskType: EasyLoadingMaskType.black);
    try {
      var result = await _deletePost(postId: postId);
      if (!(result ?? false)) {
        EasyLoading.showError(L.delete_fail.tr);
        return;
      }
      // 删除帖成功，更新页面
      EasyLoading.showSuccess(L.delete_success.tr);
      if (needBack) {
        // 当在帖详情页面操作删除
        Get.back();
      }
      _removeSpecificPost(postId);
    } catch (e) {
      EasyLoading.showError(L.delete_fail.tr);
      AppLogger.e("Error Catch<onDeletePostTap>: $e");
    }
  }

  void onLikePostTap(String? postId) {
    if (postId == null || postId.isEmpty) return;
    Post post = moments.firstWhere((moment) => moment.id == postId);
    _updateSpecificPostLike(post); // 更新本地数据&UI状态
    EasyDebounce.debounce(  // Idle待超过一定时间，才执行
      'moment-post-like',                 
      Duration(milliseconds: 500),    
      () {
        if (post.isLike!) {
          _likePost(postId: postId);
        } else {
          _unlikePost(postId: postId);
        }
      }              
    );
  }

  void onCommentPostTap(String? postId) {
    if (postId == null) return;
    Get.toNamed(Routes.MomentDetailView,
        arguments: {"post_id": postId, "request_focus": true});
  }

  void onSharePostTap(Post post) {
    Get.toNamed(Routes.MomentShareView, arguments: {"post": post});
  }

  void onDeleteCommentTap(String? postId, String? commentId) async {
    bool? isConfirm = await showConfirmationDialog(
        L.delete_comment_confirmation_desc.tr, L.confirm.tr);
    if (!(isConfirm ?? false)) return;
    if (postId == null || postId.isEmpty) return;
    if (commentId == null || commentId.isEmpty) return;
    EasyLoading.show(maskType: EasyLoadingMaskType.black);
    try {
      var result = await deleteComment(postId: postId, commentId: commentId);
      if (!(result ?? false)) {
        EasyLoading.showError(L.delete_fail.tr);
        return;
      }
      // 删除成功，更新页面
      EasyLoading.showSuccess(L.delete_success.tr);
      _removeSpecificComment(postId, commentId);
    } catch (e) {
      EasyLoading.showError(L.delete_fail.tr);
      AppLogger.e("Error Catch<onDeleteCommentTap>: $e");
    }
  }

  void onViewMoreCommentTap(String? postId) {
    if (postId == null) return;
    Get.toNamed(Routes.MomentDetailView, arguments: {"post_id": postId});
  }

  void onPostTap(String? postId) {
    if (postId == null) return;
    Get.toNamed(Routes.MomentDetailView, arguments: {"post_id": postId});
  }

  /// 跳转联系人详情页面
  void onGoToMemberDetail({String? username}) async {
    if (username == null) {
      return;
    }
    _channelDetailController.onGotoDetail(await getMemberData(username), _groupId);
  }

  Future<Post?> openCreatePostView({Post? post}) async {
    final result = await Get.toNamed(
      Routes.CreatePostView,
      arguments: {
        "group_id": _groupId,
        "post": post, // 只有编辑帖，才会有
      },
    );
    return result!=null ? result as Post : null;
  }

  bool isAdminOrOwner() {
    return _channelDetailController.isAdminOrOwner();
  }

  Post getPostById(String id) {
    return moments.firstWhere((element) => element.id == id);
  }

  void refreshSpecificPost(Post post) {
    if(post.id == null) return;
    final postToRefresh = getPostById(post.id!);
    postToRefresh.content = post.content;
    postToRefresh.links = post.links;
    postToRefresh.imageUrls = post.imageUrls;
    postToRefresh.videoUrl = post.videoUrl;
    postToRefresh.videoThumbnail = post.videoThumbnail;
    moments.refresh();
  }

  void _removeSpecificPost(String postId) {    
    moments.removeWhere((post) => post.id == postId);
    moments.refresh();
  }

  void _removeSpecificComment(String postId, String commentId) {    
    Post post = getPostById(postId);
    if(post.commentPaginationModel == null) return;
    if(post.commentPaginationModel!.comments == null) return;
    post.commentPaginationModel!.comments!.removeWhere((comment) => comment.id == commentId);
    moments.refresh();
  }

  void _updateSpecificPostLike(Post post) {
    if (post.isLike == null) return;
    post.isLike = !(post.isLike!); // 更换状态
    if (post.isLike!) {
      // 增减点赞数
      post.likeCount = post.likeCount! + 1;
    } else {
      post.likeCount = post.likeCount! - 1;
    }
    moments.refresh();
  }

  Future<void> _fetchPosts({String? pageNo}) async {
    isLoading.value = true;
    try {
      var data = await ChannelMomentApi.getPosts(
          headers: apiHeaders(), pageNo: pageNo);
      _postPaginationModel = data;
      if(_postPaginationModel == null || _postPaginationModel?.posts == null) return;
      for(var post in _postPaginationModel!.posts!){
        // 通过username去获取nickname & avatarUrl
        if (post.user?.userName != null) {
          MemberInfo info = await getMemberData(post.user!.userName!);
          post.user?.nickname = info.nickname;
          post.user?.avatarUrl = info.avatarPath;
        }
        if (post.commentPaginationModel?.comments?.isNotEmpty ?? false) {
          for(var comment in post.commentPaginationModel!.comments!){
            if (comment.user?.userName != null) {
              MemberInfo info = await getMemberData(comment.user!.userName!);
              comment.user?.nickname = info.nickname;
              comment.user?.avatarUrl = info.avatarPath;
            }
          }
        }
        // // 获取视频缩略图比例
        // if(post.videoThumbnail != null && post.videoThumbnail!.isNotEmpty){
        //   await post.addVideoThumbnailRatio();
        // }
      }
      if (_postPaginationModel?.currentPage == 1) {
        moments.clear();
      }
      moments.addAll(_postPaginationModel?.posts ?? <Post>[].obs);
    } catch (e) {
      AppLogger.e("Error Catch <_fetchPosts>: $e");
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool?> _deletePost({required String postId}) async {
    try {
      return await ChannelMomentApi.deletePost(
        headers: apiHeaders(),
        postId: postId,
      );
    } catch (e) {
      AppLogger.e("Error Catch <_deletePost>: $e");
      return null;
    }
  }

  Future<bool?> deleteComment(
      {required String postId, required String commentId}) async {
    try {
      return await ChannelMomentApi.deleteComment(
        headers: apiHeaders(),
        postId: postId,
        commentId: commentId,
      );
    } catch (e) {
      AppLogger.e("Error Catch <deleteComment>: $e");
      return null;
    }
  }

  Future<bool?> _likePost({required String postId}) async {
    try {
      return await ChannelMomentApi.likePost(
        headers: apiHeaders(),
        postId: postId,
      );
    } catch (e) {
      AppLogger.e("Error Catch <_likePost>: $e");
      return null;
    }
  }

  Future<bool?> _unlikePost({required String postId}) async {
    try {
      return await ChannelMomentApi.unlikePost(
        headers: apiHeaders(),
        postId: postId,
      );
    } catch (e) {
      AppLogger.e("Error Catch <_unlikePost>: $e");
      return null;
    }
  }

  Future<void> _getMomentInfo() async {
    try {
      String? desc = await ChannelMomentApi.getMomentInfo(
        headers: apiHeaders(),
      );
      if (desc == null) {
        description.value = '';
        return;
      }
      description.value = desc;
    } catch (e) {
      AppLogger.e("Error Catch <_getMomentInfo>: $e");
      return null;
    }
  }

  Future<bool?> _updateMomentInfo(
      {required String desc, required String groupName}) async {
    try {
      return await ChannelMomentApi.updateMomentInfo(
        headers: apiHeaders(),
        groupName: groupName,
        description: desc,
      );
    } catch (e) {
      AppLogger.e("Error Catch <_updateMomentInfo>: $e");
      return null;
    }
  }

  Future<bool?> showConfirmationDialog(
      String dialogDesc, String positiveBtnText) async {
    return await SmartDialog.show(builder: (context) {
      return AlertDialog(
        content: Text(dialogDesc),
        actions: [
          TextButton(
            child: Text(L.cancel.tr),
            onPressed: () => SmartDialog.dismiss(), //关闭对话框,
          ),
          TextButton(
            child: Text(positiveBtnText),
            onPressed: () async {
              SmartDialog.dismiss(result: true);
            }, //关闭对话框,
          ),
        ],
        actionsAlignment: MainAxisAlignment.end,
      );
    });
  }

  Future<String?> _showEditDescDialog({String? initialDesc}) async {
    TextEditingController _controller =
        TextEditingController(text: initialDesc);
    return await SmartDialog.show(builder: (context) {
      return AlertDialog(
        title: Text(
          L.update_description_dialog_title.tr,
          style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w500),
        ),
        content: SizedBox(
          width: 1.sw,
          child: TextField(
            controller: _controller,
            maxLines: 5,
            maxLength: 10000,
            decoration: InputDecoration(
                hintText: L.update_description_dialog_hint.tr,
                border: OutlineInputBorder()),
          ),
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => SmartDialog.dismiss(),
            child: Text(L.cancel.tr, style: TextStyle(fontSize: 14.sp)),
          ),
          TextButton(
            onPressed: () => SmartDialog.dismiss(result: _controller.text),
            child: Text(L.edit.tr, style: TextStyle(fontSize: 14.sp)),
          ),
        ],
      );
    });
  }

  Map<String, String> apiHeaders() {
    String usernameWithoutNode =
        _removeAfterSymbol(_username, "@"); // 只保存节点前的ID
    return {GROUP_ID: _groupId, USERNAME: usernameWithoutNode};
  }

  // ------------------ 获取成员数据部分 ------------------
  /// 通过username(id)获取用户的nickname和头像
  ///
  /// 成员数据
  Future<MemberInfo> getMemberData(String id) async {
    if (id.contains("@")) {
      id = _removeAfterSymbol(id, "@");
    }
    // 此处'id'为无节点id
    MemberInfo data = MemberInfo(id, nickname: L.member.tr);
    // 先查找缓存
    var newMemberCacheMap = _mapMemberCacheIdWithoutNode(
        _channelDetailController.memberAvatarCache);
    if (newMemberCacheMap.containsKey(id)) {
      // 当缓存有数据
      data = newMemberCacheMap[id]!;
    } else {
      // 当缓存没有数据，查找本地
      var contactData =
        await Get.find<AppDatabase>().oneContact(id).getSingleOrNull();
      if(contactData != null){
        data = MemberInfo(id, nickname: contactData.displayname, avatarPath: contactData.avatarPath);
      }      
      // var newMemberDatasMap = _mapGroupMemberDataWithoutNode(
      //     _channelDetailController.rxMemberDatas);
      // if (newMemberDatasMap.containsKey(id)) {
      //   // 当成员表有数据
      //   data = _channelDetailController.toMemberInfo(newMemberDatasMap[id]!);
      // } 
    }
    if (data.name == _channelDetailController.myName) {
      // 若id为自己，使用displayName, 避免出现nickname从contact拿时为"My Computer"问题
      data.nickname = Get.find<AppConfigService>().getMySelfDisplayName();
    }
    return data;
  }

  Map<String, MemberInfo?> _mapMemberCacheIdWithoutNode(
      Map<String, MemberInfo?> memberCache) {
    return memberCache.map((key, value) {
      String newKey = _removeAfterSymbol(key, "@"); // key: 刪除key"@"符号后的节点
      return MapEntry(newKey, value);
    });
  }

  // Map<String, GroupMemberData> _mapGroupMemberDataWithoutNode(
  //     Map<String, GroupMemberData> groupMemberData) {
  //   return groupMemberData.map((key, value) {
  //     String newKey = _removeAfterSymbol(key, "@"); // key: 刪除key"@"符号后的节点
  //     return MapEntry(newKey, value);
  //   });
  // }

  String _removeAfterSymbol(String str, String symbol) {
    int index = str.indexOf(symbol);
    if (index != -1) {
      return str.substring(0, index);
    }
    return str;
  }
  // ------------------ 获取成员数据部分 结束 ------------------
}
