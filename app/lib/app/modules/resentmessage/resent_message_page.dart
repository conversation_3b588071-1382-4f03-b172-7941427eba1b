import 'package:extended_text/extended_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/modules/resentmessage/resent_message_controller.dart';
// import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/app/widgets/divider_cus.dart';
import 'package:flutter_metatel/app/widgets/select_contact_page.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../r.dart';
import '../../widgets/at_widget/my_special_text_span_builder.dart';
import '../../widgets/brown_app_bar.dart';

class ResentMessagePage extends StatefulWidget {
  final String? title;
  final bool showCreateChat;

  final SelectContactType type;
  final List<ContactData>? contactDatas;
  final bool showGroup;

  const ResentMessagePage({
    super.key,
    this.contactDatas,
    this.title,
    this.showCreateChat = true,
    this.type = SelectContactType.forwarding,
    this.showGroup = true,
  });

  @override
  State<StatefulWidget> createState() => _ResentMessagePage();
}

class _ResentMessagePage extends State<ResentMessagePage> {
  final RecentMessageController _controller =
      Get.put(RecentMessageController());

  @override
  Widget build(BuildContext context) {
    return BrownAppBar(
      title: widget.title ?? L.message_dialog_forward.tr,
      bgColor: AppColors.colorFF28231A,
      dottedColor: AppColors.colorFF6E5F4E,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          image: DecorationImage(image: AssetImage(R.settingBg), fit: BoxFit.cover),
        ),
        child: Column(
          children: [
            Visibility(
              visible: widget.showCreateChat,
              child: GestureDetector(
                onTap: () async {
                  var res=await Get.to(SelectContactPage(
                    type: widget.type,
                    contactDatas: widget.contactDatas,
                    showGroup: widget.showGroup,
                  ));
                  if(res!=null){
                    Get.back(result: res);
                  }
                },
                child: Container(
                  padding: const EdgeInsets.only(top: 10, bottom: 10,left: 16,right: 16).r,
                  alignment: Alignment.centerLeft,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        L.share_creat_new_chat.tr,
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.colorFF1F1B1C,
                        ),
                      ),
                      const Icon(
                        Icons.chevron_right,
                        size: 25,
                      )
                    ],
                  ),
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.only(left: 16,right: 16,top: 8,bottom: 8).r,
              width: double.infinity,
              color:AppColors.colorFF28231A.withOpacity(0.3),
              child: Text(
                L.share_latest.tr,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.colorFF1F1B1C,
                ),
              ),
            ),
            Expanded(
              child: Obx(
                () => Container(
                  child: ScrollConfiguration(
                    behavior: ScrollBehavior().copyWith(overscroll: false),
                    child: ListView.builder(
                      physics: const AlwaysScrollableScrollPhysics(),
                      itemCount: _controller.allSessionDataObs.length,
                      itemBuilder: (context, index) {
                        var sessionData = _controller.allSessionDataObs[index];
                        return recentMessageListItem(sessionData: sessionData);
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
    
    // Scaffold(
    //   appBar: AppBarCommon()
    //       .build(context, title: widget.title ?? L.message_dialog_forward.tr),
    //   body: Column(
    //     children: [
    //       Visibility(
    //         visible: widget.showCreateChat,
    //         child: GestureDetector(
    //           onTap: () async {
    //             var res=await Get.to(SelectContactPage(
    //               type: widget.type,
    //               contactDatas: widget.contactDatas,
    //               showGroup: widget.showGroup,
    //             ));
    //             if(res!=null){
    //               Get.back(result: res);
    //             }
    //           },
    //           child: Container(
    //             padding: const EdgeInsets.only(top: 10, bottom: 10,left: 16,right: 16).r,
    //             alignment: Alignment.centerLeft,
    //             child: Row(
    //               mainAxisSize: MainAxisSize.min,
    //               children: [
    //                 Text(
    //                   L.share_creat_new_chat.tr,
    //                   style: TextStyle(
    //                     fontSize: 18.sp,
    //                   ),
    //                 ),
    //                 const Icon(
    //                   Icons.chevron_right,
    //                   size: 25,
    //                 )
    //               ],
    //             ),
    //           ),
    //         ),
    //       ),
    //       Container(
    //         padding: const EdgeInsets.only(left: 16,right: 16,top: 8,bottom: 8).r,
    //         width: double.infinity,
    //         color:AppColors.colorDivider,
    //         child: Text(
    //           L.share_latest.tr,
    //           style: const TextStyle(
    //           ),
    //         ),
    //       ),
    //       Expanded(
    //         child: Obx(
    //           () => Container(
    //             child: ListView.builder(
    //               physics: const AlwaysScrollableScrollPhysics(),
    //               itemCount: _controller.allSessionDataObs.length,
    //               itemBuilder: (context, index) {
    //                 var sessionData = _controller.allSessionDataObs[index];
    //                 return recentMessageListItem(sessionData: sessionData);
    //               },
    //             ),
    //           ),
    //         ),
    //       ),
    //     ],
    //   ),
    // );
  }

  String _getDisplayName(SessionData sessionData){
    String ret=sessionData.displayname??"";
    if(sessionData.chatType == ChatType.channelChat.index){
      // var sessionController = Get.find<SessionController>();
      // if (ChannelAttribute.dao ==
      //     sessionController.channelAttribute(sessionData.username)) {
      //     ret=getDaoDisplayName(sessionData.displayname ?? '');
      // }else if (ChannelAttribute.vip ==
      //     sessionController.channelAttribute(sessionData.username)){
      //    ret=getVipDisplayName(sessionData.displayname ?? '');
      // }
    }else if(sessionData.chatType == ChatType.officialChat.index){
      ret=getOfficeDisplayName();
    } else if(sessionData.chatType == ChatType.singleChat.index){
      // ret=getDisplayNameOffTid(ret,isTid: sessionData.isTid);
    }
    return ret;
  }

  Widget recentMessageListItem({
    required SessionData sessionData,
  }) {
    String textName = _getDisplayName(sessionData);
    if (sessionData.chatType == ChatType.singleChat.index && textName.isEmpty) {
      textName = sessionData.username.substring(6);
    }
    int msgTime = sessionData.time ?? 0;
    if (msgTime <= 0 && sessionData.createTime != null) {
      msgTime = sessionData.createTime!.toInt();
    }
    return InkWell(
      onTap: ()=>_controller.onTap(sessionData),
      child: Container(
        // color: AppColors.transparent,
        padding: const EdgeInsets.only(left: 16,right: 16,).r,
        height: 89.r,
        child: Row(
          children: [
            buildChatAvatarWithAttr(sessionData.chatType??0, sessionData.username,
              diameter: 50,
              text: textName,
              imagePath: appSupporAbsolutePath(sessionData.avatarPath),
              textStyle: const TextStyle(
                color: Colors.white,
                fontSize: 22,
                fontWeight: FontWeight.normal,
              ),
            ),
            // 间隔
            SizedBox(width: 15.r),
            Expanded(
              child: Column(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // 名称/时间行
                        Row(
                          children: [
                            // 名称
                            Expanded(
                              child: ExtendedText(
                                getDisplayNameOffTid(textName,isTid: sessionData.isTid,user: sessionData.username),
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 17.sp,
                                  color: AppColors.colorFF1F1B1C,
                                  fontWeight: FontWeight.bold,
                                ),
                                specialTextSpanBuilder: MySpecialTextSpanBuilder(
                                  showAtBackground: false, size: Size(15.r, 15.r),
                                ),
                              ),
                            ),
                            SizedBox(width: 10.r),
                            // 时间
                            Text(
                              msgTimeFormat(msgTime),
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: AppColors.colorFF1F1B1C,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const DividerCus(
                    thickness: 0.5,
                    color: AppColors.colorFF6E5F4E,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  @override
  void dispose() {
    Get.delete<RecentMessageController>();
    super.dispose();
  }
}
