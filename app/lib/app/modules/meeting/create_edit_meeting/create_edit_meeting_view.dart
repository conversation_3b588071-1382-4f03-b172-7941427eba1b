import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/meeting/create_edit_meeting/create_edit_meeting_controller.dart';
import 'package:flutter_metatel/app/widgets/button/wood_button.dart';
// import 'package:flutter_metatel/app/widgets/gradient_button.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../core/values/colors.dart';
import '../../../widgets/brown_app_bar.dart';
import '../../../widgets/brown_text_field.dart';

class CreateEditMeetingView extends GetView<CreateEditMeetingController> {
  const CreateEditMeetingView({super.key});

  @override
  Widget build(BuildContext context) {
    return BrownAppBar(
      title: controller.isEditView()
        ? L.edit_meeting.tr
        : L.create_meeting_2_cap.tr,
      resizeToAvoidBottomInset: false,
      onEmptySpaceTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          // color: Colors.white.withOpacity(0.9),
          image: DecorationImage(image: AssetImage(R.meetingBg), fit: BoxFit.cover),
        ),
        child: _buildBody(context),
      ),
    );
    // Scaffold(
    //   backgroundColor: AppColors.backgroundGray,
    //   appBar: AppBar(
    //     title: controller.isEditView()
    //         ? Text(L.edit_meeting.tr)
    //         : Text(L.create_new_meeting.tr),
    //     elevation: 1,
    //   ),
    //   body: _buildBody(context),
    // );
  }

  Widget _buildBody(BuildContext context) {
    return Container(
      height: 1.sh - kToolbarHeight,
      padding: EdgeInsets.symmetric(horizontal: 25.r),
      child: ScrollConfiguration(
        behavior: ScrollBehavior().copyWith(overscroll: false),
        child: SingleChildScrollView(
          child: Align(
            alignment: Alignment.topCenter,
            child: Container(
              width: 378.r,
              child: Form(
                key: controller.formKey,
                child: Obx(
                  () => Column(
                    // crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 15.r),
                          
                      /// 会议标题
                      BrownTextField(
                        label: L.meeting_title.tr,
                        controller: controller.titleEditingController,
                        maxLength: 40,
                        textInputAction: TextInputAction.next,
                        hintText: L.hint_title.tr,
                        // decoration: _buildInputDecoration(L.hint_title.tr),
                        // validator: controller.titleValidator,
                        textFieldWidth: 378.r,
                        showCounterText: true,
                      ),
                      // TextFormField(
                      //   controller: controller.titleEditingController,
                      //   maxLength: 40,
                      //   textInputAction: TextInputAction.next,
                      //   decoration: _buildInputDecoration(L.hint_title.tr),
                      //   validator: controller.titleValidator,
                      // ),
                      SizedBox(height: 25.r),
                          
                      /// 会议描述
                      BrownTextField(
                        label: L.meeting_description.tr,
                        controller: controller.descEditingController,
                        maxLength: 200,
                        maxLines: 6,
                        textInputAction: TextInputAction.done,
                        hintText: L.hint_description.tr,
                        textFieldWidth: 378.r,
                        textFieldHeight: 130.r,
                        showCounterText: true,
                      ),
                      // _buildLabel(L.meeting_description.tr),
                      // SizedBox(height: 5.r),
                      // TextFormField(
                      //   controller: controller.descEditingController,
                      //   maxLength: 200,
                      //   maxLines: 5,
                      //   textInputAction: TextInputAction.done,
                      //   decoration: _buildInputDecoration(L.hint_description.tr),
                      // ),
                      SizedBox(height: 25.r),
                          
                      /// 日期
                      Align(
                        alignment: Alignment.centerLeft,
                        child: _buildLabel(L.meeting_date.tr),
                      ),
                      SizedBox(height: 5.r),
                      _buildDatePicker(
                        content: controller.selectedDate.value == null
                            ? "MM/DD/YYYY"
                            : DateFormat('MM/dd/yyyy')
                                .format(controller.selectedDate.value!),
                        onTap: () async {
                          DateTime? selectedDateTime = await showDatePicker(
                            context: context,
                            firstDate: controller.validStartDate,
                            lastDate: controller.validEndDate,
                            // barrierColor:AppColors.backgroundGray,
                            initialDate: controller.isValidDateRange(
                                    controller.selectedDate.value!)
                                ? controller.selectedDate.value
                                : controller.validStartDate,
                            builder: (context, child) {
                              return _buildDateTimePickerTheme(
                                context,
                                child!,
                              );
                            },
                          );
                          controller.onHandleDate(selectedDateTime);
                        },
                      ),
                      if (controller.dateErrorMsg.value.isNotEmpty)
                        SizedBox(height: 8.r),
                      if (controller.dateErrorMsg.value.isNotEmpty)
                        Text(
                          controller.dateErrorMsg.value,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.red[900],
                          ),
                        ),
                      SizedBox(height: 25.r),
                          
                      /// 时间
                      Align(
                        alignment: Alignment.centerLeft,
                        child: _buildLabel(L.meeting_time.tr),
                      ),
                      SizedBox(height: 5.r),
                      SizedBox(
                        width: 378.r,
                        child: Row(
                          children: [
                            /// 开始时间
                            Expanded(
                              child: _buildTimePicker(
                                controller.selectedStartTime.value == null
                                    ? L.start_time.tr
                                    : "${controller.selectedStartTime.value!.format(context)}",
                                onTap: () async {
                                  TimeOfDay? timeOfDay = await showTimePicker(
                                    context: context,
                                    // barrierColor:AppColors.colorFF1D1D1D,
                                    initialTime: controller.selectedStartTime.value ??
                                        TimeOfDay(
                                            hour: TimeOfDay.now().hour + 1, minute: 0),
                                    builder: (context, child) {
                                      return _buildDateTimePickerTheme(
                                        context,
                                        child!,
                                      );
                                    },
                                  );
                                  controller.onHandleStartTime(timeOfDay);
                                },
                              ),
                            ),
                            SizedBox(width: 7.r),
                            SizedBox(
                              width: 20.r,
                              child: Center(
                                child: Text(
                                  L.to.tr,
                                  style: TextStyle(
                                    fontSize: 15.sp,
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.colorFF6E5F4E,
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 7.r),
                            
                            /// 结束时间
                            Expanded(
                              child: _buildTimePicker(
                                controller.selectedEndTime.value == null
                                    ? L.end_time.tr
                                    : "${controller.selectedEndTime.value!.format(context)}",
                                onTap: () async {
                                  TimeOfDay? timeOfDay = await showTimePicker(
                                    context: context,
                                    // barrierColor:AppColors.colorFF1D1D1D,
                                    initialTime: controller.selectedEndTime.value ??
                                        (controller.selectedStartTime.value != null
                                            ? TimeOfDay(
                                                hour: controller
                                                        .selectedStartTime.value!.hour +
                                                    1,
                                                minute: controller
                                                    .selectedStartTime.value!.minute,
                                              )
                                            : TimeOfDay(
                                                hour: TimeOfDay.now().hour + 2,
                                                minute: 0,
                                              )),
                                    builder: (context, child) {
                                      return _buildDateTimePickerTheme(
                                        context,
                                        child!,
                                      );
                                    },
                                  );
                                  controller.onHandleEndTime(timeOfDay);
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (controller.timeErrorMsg.value.isNotEmpty)
                        SizedBox(height: 8.r),
                      if (controller.timeErrorMsg.value.isNotEmpty)
                        Text(
                          controller.timeErrorMsg.value,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.red[900],
                          ),
                        ),
                      SizedBox(height: 26.r),
                          
                      /// 设置密码Checkbox
                      _buildCheckBox(
                        isChecked: controller.isPasscode.value,
                        desc: L.passcode_desc.tr,
                        desc2: L.passcode_desc_2.tr,
                        onTap: () {
                          controller.isPasscode.value =
                              !controller.isPasscode.value;
                          if (!controller.isPasscode.value) {
                            controller.passcodeEditingController.clear();
                          }
                        },
                      ),
                          
                      
                        // Padding(
                        //   padding: const EdgeInsets.only(left: 30).r,
                        //   child: Row(
                        //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        //     children: [
                        //       _buildLabel(L.passcode.tr),
                        //       TextButton(
                        //         onPressed: controller.onGeneratePasscodeTap,
                        //         child: Text(
                        //           L.generate_meeting_passcode.tr,
                        //           style: TextStyle(
                        //             decoration: TextDecoration.underline,
                        //             fontSize: 12.sp,
                        //             color: AppColors.white
                        //           ),
                        //         ),
                        //       ),
                        //     ],
                        //   ),
                        // ),
                      /// 设置密码
                      if (controller.isPasscode.value) SizedBox(height: 8.r),
                      if (controller.isPasscode.value)
                        Padding(
                          padding: const EdgeInsets.only(left: 30).r,
                          child: BrownTextField(
                            label: L.meeting_passcode.tr,
                            controller: controller.passcodeEditingController,
                            maxLength: 8,
                            obscureText: controller.isObscure.value,
                            textInputAction: TextInputAction.done,
                            keyboardType: TextInputType.visiblePassword,
                            hintText: L.passcode.tr,
                            
                            // decoration: _buildInputDecoration(L.hint_title.tr),
                            // validator: controller.titleValidator,
                            textFieldWidth: 346.r,
                            suffixIcon: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                TextButton(
                                  onPressed: controller.onGeneratePasscodeTap,
                                  child: Text(
                                    L.generate_meeting_passcode.tr,
                                    style: TextStyle(
                                      fontSize: 15.sp,
                                      color: AppColors.colorFFCAB692,
                                    ),
                                  ),
                                ),
                                IconButton(
                                  onPressed: () {
                                    controller.isObscure.value =
                                        !controller.isObscure.value;
                                  },
                                  icon: Icon(
                                    controller.isObscure.value
                                        ? Icons.visibility_off
                                        : Icons.visibility,
                                    size: 22.r,
                                    color: AppColors.colorFFCAB692,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      // if (controller.isPasscode.value)
                      //   TextFormField(
                      //     controller: controller.passcodeEditingController,
                      //     maxLength: 8,
                      //     obscureText: controller.isObscure.value,
                      //     textInputAction: TextInputAction.done,
                      //     keyboardType: TextInputType.visiblePassword,
                      //     decoration: _buildInputDecoration(
                      //       L.passcode.tr,
                      //       counterText: "",
                      //       suffix: IconButton(
                      //         onPressed: () {
                      //           controller.isObscure.value =
                      //               !controller.isObscure.value;
                      //         },
                      //         icon: Icon(
                      //           controller.isObscure.value
                      //               ? Icons.visibility_off
                      //               : Icons.visibility,
                      //           size: 22.r,
                      //         ),
                      //       ),
                      //     ),
                      //     validator: controller.passcodeValidator,
                      //   ),
                          
                      SizedBox(height: 30.r),
                          
                      /// 设置任何用户能打开会议Checkbox
                      /// 默认为不允许
                      _buildCheckBox(
                        isChecked: controller.isAnyUserCanOpenMeeting.value,
                        desc: L.any_user_can_open_meeting.tr,
                        desc2: L.any_user_can_open_meeting_2.tr,
                        onTap: () {
                          controller.isAnyUserCanOpenMeeting.value =
                              !controller.isAnyUserCanOpenMeeting.value;
                        },
                      ),
                          
                      SizedBox(height: 50.r),
                          
                      /// 提交按钮
                      Center(
                        child: WoodButton(
                          buttonWidth: 133.r,
                          onTap: controller.isEditView()
                            ? controller.onUpdateTap
                            : controller.onSubmitTap,
                          text: controller.isEditView()
                              ? L.confirm.tr
                              : L.submit_1_cap.tr,
                        ),
                      ),
                      
                      // Center(child: GradientButton(
                      //   onPressed: controller.isEditView()
                      //       ? controller.onUpdateTap
                      //       : controller.onSubmitTap,
                      //   child: Text(
                      //     controller.isEditView()
                      //         ? L.confirm.tr
                      //         : L.submit_1_cap.tr,
                      //     style: TextStyle(
                      //       fontWeight: FontWeight.w400,
                      //     ),
                      //   ),
                      // ),),
                          
                      if (controller.isEditView()) SizedBox(height: 6.r),
                          
                      /// 取消会议按钮
                      if (controller.isEditView())
                        Center(
                          child: TextButton(
                            onPressed: controller.onCancelTap,
                            style: TextButton.styleFrom(
                              foregroundColor: AppColors.colorNegativeBtn,
                              textStyle: TextStyle(
                                fontSize: 15.sp,
                              ),
                            ),
                            child: Text(
                              L.cancel_meeting.tr,
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                        ),
                          
                      SizedBox(height: 50.r),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLabel(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10).r,
      child: Text(
        text,
        style: TextStyle(
          fontSize: 17.sp,
          fontWeight: FontWeight.w500,
          color: AppColors.colorFF6E5F4E,
        ),
      ),
    );
  }

  // InputDecoration _buildInputDecoration(String hintText,
  //     {Widget? suffix, String? counterText}) {
  //   return InputDecoration(
  //     hintText: hintText,
  //     hintStyle: TextStyle(
  //       fontSize: 13.sp,
  //       fontWeight: FontWeight.w300,
  //     ),
  //     counterText: counterText,
  //     contentPadding: EdgeInsets.symmetric(horizontal: 5.r, vertical: 10.r),
  //     enabledBorder: UnderlineInputBorder(
  //       borderSide: BorderSide(color: AppColors.colorFFCBCBCB),
  //     ),
  //     suffix: suffix,
  //   );
  // }

  Widget _buildCheckBox(
      {required bool isChecked,
      required Function()? onTap,
      required String desc, required String desc2}) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTap,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 18.r,
            height: 18.r,
            decoration: BoxDecoration(
              color: isChecked ? AppColors.colorFF83623E : AppColors.transparent,
              shape: BoxShape.circle,
              border: Border.all(color: isChecked ? AppColors.colorFFCAB692 : AppColors.colorFF707070, width: isChecked ? 0.5.r : 1.r),
            ),
            child: isChecked
                ? Center(
                    child: Icon(
                      Icons.check,
                      size: 16.r,
                      color: AppColors.white,
                    ),
                  )
                : const SizedBox.shrink(),
          ),
          SizedBox(width: 12.r),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  desc,
                  textAlign: TextAlign.left,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 17.sp,
                    color: AppColors.colorFF6E5F4E,
                  ),
                ),
                Text(
                  desc2,
                  textAlign: TextAlign.left,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 15.sp,
                    color: AppColors.colorFF6E5F4E,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDatePicker({required String content, Function()? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 378.r,
        height: 45.r,
        padding: EdgeInsets.symmetric(horizontal: 20.r, vertical: 10.r),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(5).r),
          gradient: LinearGradient(
            colors: [
              AppColors.colorFFC2AF8C,
              AppColors.colorFF83623E,
            ],
          ),
        ),
        child: Row(
          children: [
            Text(
              content,
              style: TextStyle(
                fontSize: 17.sp,
                fontWeight: FontWeight.w300,
                color: controller.selectedDate.value == null
                    ? AppColors.colorFFE6E1DD
                    : Colors.white,
              ),
            ),
            Spacer(),
            Image.asset(
              R.iconMeetingCalendar,
              width: 20.r,
              height: 20.r,
              color: AppColors.colorFFE6E1DD,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimePicker(String label, {Function()? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        // width: (1.sw - 30.r - 30.r - 20.r) / 2,
        height: 46.r,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(5).r),
          gradient: LinearGradient(
            colors: [
              AppColors.colorFFC2AF8C,
              AppColors.colorFF83623E,
            ],
          ),
        ),
        child: Center(
          child: Row(
            children: [
              SizedBox(width: 20.r),
              Text(
                label,
                style: TextStyle(
                  fontSize: 17.sp,
                  color: AppColors.colorFFE6E1DD,
                ),
              ),
              // Spacer(),
              // Icon(
              //   Icons.keyboard_arrow_down_rounded,
              //   size: 10.r,
              //   color: AppColors.colorFF656565,
              // ),
              SizedBox(width: 10.r),
            ],
          ),
        ),
      ),
    );
  }

  Theme _buildDateTimePickerTheme(BuildContext context, Widget child) {
    return Theme(
      data: Theme.of(context).copyWith(
        colorScheme: const ColorScheme.light(
          primary: AppColors.colorFF6E5F4E,
        ),
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            foregroundColor: AppColors.colorFFE6E1DD,
          ),
        ),
        textTheme: const TextTheme(
          titleMedium: TextStyle(color: AppColors.colorFFE6E1DD),
        ),
        timePickerTheme: const TimePickerThemeData(
          backgroundColor:AppColors.colorFF6E5F4E,
          dialTextColor: AppColors.colorFFE6E1DD,
          dayPeriodTextColor: AppColors.colorFFE6E1DD,
          dayPeriodColor: AppColors.colorFF83623E,
          hourMinuteTextColor: AppColors.colorFFE6E1DD,
        ),
        datePickerTheme: const DatePickerThemeData(
          backgroundColor: AppColors.colorFFD5BFA7,
          confirmButtonStyle: ButtonStyle(
            foregroundColor: MaterialStatePropertyAll(AppColors.colorFF83623E),
          ),
          cancelButtonStyle: ButtonStyle(
            foregroundColor: MaterialStatePropertyAll(AppColors.colorFF83623E),
          ),
        ),
      ),
      child: child,
    );
  }
}
