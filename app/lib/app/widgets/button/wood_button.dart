import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/values/colors.dart';
import '../../../r.dart';

class WoodButton extends StatelessWidget {
  const WoodButton({super.key, this.buttonWidth = 131, this.buttonHeight = 46, this.text = "confirm", this.onTap, this.icon, this.suffixIcon});
  final double buttonWidth;
  final double buttonHeight;
  final String text;
  final String? icon;
  final String? suffixIcon;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: buttonWidth.r,
        height: buttonHeight.r,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(R.buttonBg),
            fit: BoxFit.fill,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if(icon != null)
              Image.asset(
                icon!,
                width: 17.r,
                height: 17.r,
              ),
            if(icon != null)
              SizedBox(width: 8.29.r),
            Text(
              text.tr,
              style: TextStyle(
                fontSize: 17.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.colorFFCAB692,
              ),
            ),
            if(suffixIcon != null)
              SizedBox(width: 3.r),
            if(suffixIcon != null)
              Image.asset(
                suffixIcon!,
                width: 22.r,
                height: 22.r,
              ),
          ],
        ),
      ),
    );
  }
}