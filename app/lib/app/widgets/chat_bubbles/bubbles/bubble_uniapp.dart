/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-05-18 10:37:12
 * @Description  : 联系人名片
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-08-15 12:17:38
 * @FilePath     : /flutter_metatel/lib/app/widgets/chat_bubbles/bubbles/bubble_contact.dart
 */

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'bubble.dart';

class BubbleUniapp extends StatelessWidget {
  final BubbleMetadata metadata;
  final Color fromNameTextColor;
  final Map<String, dynamic>? map;

  const BubbleUniapp({
    Key? key,
    required this.metadata,
    required this.fromNameTextColor,
    required this.map,
  }) : super(key: key);

  Widget _createError() {
    return Image.asset(
      R.icUniappDefault,
      width: 15.r,
      height: 15.r,
    );
  }

  @override
  Widget build(BuildContext context) {
    bool _full = false;
    Color? _bgColor = AppColors.white;
    // 参数：{
    // "name": "潮玩淘",
    // "describe": "潮玩孤品猎人，从这里出发",
    // "icon": "http://180.74.239.117:7081/icon/__UNI__609C541.png",
    // "appid": "__UNI__609C541",
    // "versionName": "1.0.3",
    // "versionCode": "103",
    // "resource": "http://180.74.239.117:7081/wgt/__UNI__609C541.wgt"
    // }
    String tatal = map?['name'] ?? '';
    String desc = map?['describe'] ?? '';
    String icon = map?['icon'] ?? '';
    String image = map?['image'] ?? '';
    String introduce = map?['introduce'] ?? '';

    Widget _child = Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r), color: AppColors.white),
      width: 171.r, // 260
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          /// 小程序
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 11.r, vertical: 10.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    CachedNetworkImage(
                      imageUrl: icon,
                      width: 15.r,
                      height: 15.r,
                      placeholder: (c, s) {
                        return _createError();
                      },
                      errorWidget: (c, s, e) {
                        return _createError();
                      },
                    ),
                    SizedBox(width: 5.r),
                    Text(
                      tatal,
                      style: TextStyle(
                        color: AppColors.colorFF000000,
                        fontSize: 10.sp,
                      ),
                    ),
                    Text(
                      ' | ',
                      style: TextStyle(
                        color: AppColors.colorFF000000,
                        fontSize: 13.sp,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        desc,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          color: AppColors.colorFF000000,
                          fontSize: 10.sp,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 6.r),
                CachedNetworkImage(
                  imageUrl: image,
                  width: 149.r,
                  height: 149.r,
                  placeholder: (c, s) {
                    return _createError();
                  },
                  errorWidget: (c, s, e) {
                    return _createError();
                  },
                ),
                SizedBox(height: 4.r),
                Text(
                  introduce,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  style: TextStyle(
                    color: AppColors.colorFF5E5E5E,
                    fontSize: 12.sp,
                  ),
                ),
                SizedBox(height: 10.r),
                Row(
                  children: [
                    Image.asset(
                      R.icUniappMsgLogo,
                      width: 11.r,
                      height: 11.r,
                    ),
                    SizedBox(width: 5.r),
                    Text(
                      L.youyuan_uniapp.tr,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      style: TextStyle(
                        color: AppColors.colorFF5E5E5E,
                        fontSize: 10.sp,
                      ),
                    )
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );

    _full = true;
    return BubbleBase(
      metadata: metadata,
      child: _child,
      // 消息内容
      tail: true,
      // 是否显示尾部
      full: _full,
      // color: _bgColor,
      fromNameTextColor: fromNameTextColor,
    );
  }
}
