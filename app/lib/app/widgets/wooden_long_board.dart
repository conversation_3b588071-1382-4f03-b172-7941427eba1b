import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/values/colors.dart';
import '../../r.dart';

class WoodenLongBoard extends StatelessWidget {
  const WoodenLongBoard({super.key, this.text, this.padding, this.child});
  final String? text;
  final EdgeInsetsGeometry? padding;
  final Widget? child;
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 370.12.r,
      height: 53.53.r,
      padding: child != null 
        ? null 
        : (padding ?? EdgeInsets.symmetric(horizontal: 23.r)),
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(R.woodenLongBoardBg),
          fit: BoxFit.fill,
        ),
      ),
      child: child ?? 
        (
          text == null 
            ? null 
            : Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  text!,
                  style: TextStyle(
                    fontSize: 17.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.colorFFC2AF8C,
                  ),
                ),
              )
        ),
    );
  }
}