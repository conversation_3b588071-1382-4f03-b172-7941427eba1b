import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/values/colors.dart';

class BrownTextField extends StatefulWidget {
  const BrownTextField({super.key, this.label, this.hintText, this.inputFormatters, this.maxLength, 
    this.controller, this.suffixIcon, this.prefixIcon, this.textAlignVertical, this.focusNode, 
    this.labelStyle, this.keyboardType, this.readOnly = false, this.textInputAction, this.textFieldWidth, this.maxLines = 1, this.showCounterText = false, this.textFieldHeight, this.obscureText = false,});
  final double? textFieldWidth;
  final double? textFieldHeight;
  final String? label;
  final TextStyle? labelStyle;
  final String? hintText;
  final List<TextInputFormatter>? inputFormatters;
  final int? maxLength;
  final int? maxLines;
  final TextEditingController? controller;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final TextAlignVertical? textAlignVertical;
  final FocusNode? focusNode;
  final TextInputType? keyboardType;
  final bool readOnly;
  final TextInputAction? textInputAction;
  final bool showCounterText;
  final bool obscureText;

  @override
  State<BrownTextField> createState() => _BrownTextFieldState();
}

class _BrownTextFieldState extends State<BrownTextField> {
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if(widget.label != null)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10).r,
            child: Text(
              widget.label!,
              style: widget.labelStyle ?? TextStyle(
                height: 1.3,
                fontSize: 17.sp,
                color: AppColors.colorFF6E5F4E,
                fontWeight: FontWeight.w500,
                // fontFamily: Config.secondFontFamily,
              ),
            ),
          ),
        if(widget.label != null)
          SizedBox(height: 5.r),
        Container(
          height: widget.textFieldHeight ??45.r,
          width: widget.textFieldWidth ?? 300.r,
          // padding: const EdgeInsets.only(
          //   left: 15,
          // ).r,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(5).r),
            gradient: LinearGradient(
              colors: [
                AppColors.colorFFC2AF8C,
                AppColors.colorFF83623E,
              ],
            ),
          ),
          child: Center(
            child: TextFormField(
              obscureText: widget.obscureText,
              readOnly: widget.readOnly,
              focusNode: widget.focusNode,
              controller: widget.controller,
              inputFormatters: widget.inputFormatters,
              maxLength: widget.maxLength,
              maxLines: widget.maxLines,
              keyboardType: widget.keyboardType,
              textAlignVertical: widget.textAlignVertical,
              cursorColor: AppColors.colorFF83623E,
              textInputAction: widget.textInputAction,
              style: TextStyle(
                fontSize: 17.sp, 
                color: AppColors.colorFFE6E1DD,
                fontWeight: FontWeight.w500, 
                // fontFamily: Config.secondFontFamily,
              ),
              // validator: widget.validator,
              // forceErrorText: null,
              onChanged: (value) {
                setState(() {
                  
                });
              },
              decoration: InputDecoration(
                contentPadding: EdgeInsets.only(left: 15.r, right: 15.r, top: 0,bottom: 0),
                isDense: true,
                hintText: widget.hintText,
                hintStyle: TextStyle(
                  fontSize: 16.sp, 
                  color: AppColors.colorFFE6E1DD,
                  fontWeight: FontWeight.w300, 
                  // fontFamily: Config.secondFontFamily,
                ),
                counterText: '',
                prefixIcon: widget.prefixIcon,
                suffixIcon: widget.suffixIcon,
                // filled: true,
                // fillColor: AppColors.colorFF151A1A,
                border: const OutlineInputBorder(
                  borderSide: BorderSide.none,
                  // borderRadius: BorderRadius.all(Radius.circular(25)),
                ),
                errorStyle: TextStyle(height: 0.01,color: Colors.transparent),
              ),
            ),
          ),
        ),
        if(widget.showCounterText)
          SizedBox(height: 8.r),
        if(widget.showCounterText)
          Align(
            alignment: Alignment.centerRight,
            child: Padding(
              padding: const EdgeInsets.only(right: 15).r,
              child: Text(
                "${widget.controller?.text.length ?? 0}/${widget.maxLength}",
                style: TextStyle(
                  height: 1.3,
                  fontSize: 15.sp,
                  color: AppColors.colorFF6E5F4E,
                  fontWeight: FontWeight.w500,
                  // fontFamily: Config.secondFontFamily,
                ),
              ),
            ),
          ),
      ],
    );
  }
}