import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../r.dart';

class SettingSwitch extends StatelessWidget {
  const SettingSwitch({super.key, this.isOn = false, this.onTap});
  final bool isOn;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Padding(
        padding: const EdgeInsets.only(left: 8, top: 8, bottom: 8).r,
        child: Container(
          width: 25.r,
          height: 15.91.r,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(isOn ? R.switchActiveBg : R.switchInactiveBg),
              fit: BoxFit.fill,
            ),
          ),
        ),
      ),
    );
  }
}